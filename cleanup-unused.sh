#!/bin/bash

echo "🧹 Cleaning up unused files..."

# Remove unused renderer directory (appears to be leftover from CRA)
if [ -d "renderer" ]; then
    echo "Removing unused renderer directory..."
    rm -rf renderer/
fi

# Remove backup files that might be taking up space
find . -name "*.backup*" -type f | head -5 | while read file; do
    echo "Found backup file: $file (consider cleaning manually)"
done

echo "✅ Cleanup suggestions provided. Review before deleting!"
