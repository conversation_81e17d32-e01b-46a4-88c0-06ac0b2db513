@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles to prevent layout issues */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Prevent text overflow and improve readability */
.break-words {
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* Ensure code blocks don't overflow */
pre {
  white-space: pre-wrap !important;
  word-break: break-all !important;
  overflow-wrap: break-word !important;
}
