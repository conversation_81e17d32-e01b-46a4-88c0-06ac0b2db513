// src/utils/errorHandler.ts

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'

export interface AppError {
  code: string
  message: string
  severity: ErrorSeverity
  context?: Record<string, any>
  timestamp: Date
}

export class ErrorHandler {
  private static logError(error: AppError) {
    const logLevel = process.env.NODE_ENV === 'development' ? 'debug' : error.severity
    
    switch (logLevel) {
      case 'critical':
        console.error(`[CRITICAL] ${error.code}: ${error.message}`, error.context)
        break
      case 'high':
        console.error(`[ERROR] ${error.code}: ${error.message}`, error.context)
        break
      case 'medium':
        console.warn(`[WARN] ${error.code}: ${error.message}`, error.context)
        break
      case 'debug':
        console.log(`[DEBUG] ${error.code}: ${error.message}`, error.context)
        break
    }
  }

  static handleApiError(error: any, context: string): AppError {
    const appError: AppError = {
      code: `API_ERROR_${context}`,
      message: error.response?.data?.error?.message || error.message || 'Unknown API error',
      severity: error.response?.status === 401 ? 'high' : 'medium',
      context: {
        status: error.response?.status,
        url: error.config?.url,
        context
      },
      timestamp: new Date()
    }

    this.logError(appError)
    return appError
  }

  static handleScreenshotError(error: any, path?: string): AppError {
    const appError: AppError = {
      code: 'SCREENSHOT_ERROR',
      message: error.message || 'Screenshot operation failed',
      severity: 'medium',
      context: { path, originalError: error.code },
      timestamp: new Date()
    }

    this.logError(appError)
    return appError
  }

  static handleProcessingError(error: any, stage: string): AppError {
    const appError: AppError = {
      code: `PROCESSING_ERROR_${stage.toUpperCase()}`,
      message: error.message || 'Processing failed',
      severity: 'high',
      context: { stage, type: error.name },
      timestamp: new Date()
    }

    this.logError(appError)
    return appError
  }
}
