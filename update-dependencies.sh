#!/bin/bash

echo "🔄 Updating Interview Coder dependencies..."

# Backup current package.json
cp package.json package.json.backup-$(date +%Y%m%d)

# Update specific packages with care
npm update @types/node @types/react @types/react-dom
npm update @typescript-eslint/eslint-plugin @typescript-eslint/parser
npm update @vitejs/plugin-react
npm update lucide-react tailwind-merge
npm update form-data

# Update Electron cautiously (major version)
echo "⚠️  Consider manually updating Electron to v37.x.x after testing"
echo "⚠️  Consider manually updating Vite to v7.x.x after testing"
echo "⚠️  Consider manually updating React to v19.x.x after testing"

echo "✅ Dependencies updated. Test the application thoroughly!"
