#!/usr/bin/env node

/**
 * Test script to verify that double API calls are prevented
 * This script simulates rapid Cmd+En<PERSON> presses to test the debouncing logic
 */

const { ProcessingHelper } = require('./dist-electron/main.js');

// Mock AppState for testing
class MockAppState {
  constructor() {
    this.view = 'queue';
    this.processingHelper = new ProcessingHelper(this);
  }

  getView() {
    return this.view;
  }

  setView(view) {
    this.view = view;
  }

  getMainWindow() {
    return {
      webContents: {
        send: (event, data) => {
          console.log(`Event sent: ${event}`, data ? `with data: ${JSON.stringify(data)}` : '');
        }
      }
    };
  }

  getScreenshotHelper() {
    return {
      getScreenshotQueue: () => ['test-screenshot.png'],
      getExtraScreenshotQueue: () => [],
      getImagePreview: () => Promise.resolve('base64-preview')
    };
  }

  PROCESSING_EVENTS = {
    NO_SCREENSHOTS: 'processing-no-screenshots',
    INITIAL_START: 'initial-start',
    INITIAL_SOLUTION_ERROR: 'solution-error'
  };
}

// Test function
async function testDoubleCallPrevention() {
  console.log('Testing double call prevention...\n');
  
  const appState = new MockAppState();
  
  console.log('1. Testing rapid successive calls (should be debounced):');
  
  // Simulate rapid successive calls
  const promises = [];
  for (let i = 0; i < 3; i++) {
    console.log(`   Call ${i + 1} initiated`);
    promises.push(appState.processingHelper.processScreenshots());
  }
  
  await Promise.all(promises);
  
  console.log('\n2. Testing call after processing is complete:');
  
  // Wait a bit and try again
  setTimeout(async () => {
    console.log('   Making another call after delay...');
    await appState.processingHelper.processScreenshots();
    console.log('\nTest completed!');
  }, 1000);
}

// Run the test if this file is executed directly
if (require.main === module) {
  testDoubleCallPrevention().catch(console.error);
}

module.exports = { testDoubleCallPrevention };
